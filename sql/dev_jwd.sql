/*
 Navicat Premium Data Transfer

 Source Server         : root@QJ-PROD
 Source Server Type    : MySQL
 Source Server Version : 80030
 Source Host           : bj-cynosdbmysql-grp-69qoyjdc.sql.tencentcdb.com:27849
 Source Schema         : dev_jwd

 Target Server Type    : MySQL
 Target Server Version : 80030
 File Encoding         : 65001

 Date: 28/07/2025 02:58:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for biz_org
-- ----------------------------
DROP TABLE IF EXISTS `biz_org`;
CREATE TABLE `biz_org` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '机构名称',
  `org_full_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构全称',
  `province` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省/直辖市代码，关联字典表',
  `city` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市/区代码，关联字典表',
  `address` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `contact_person` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `sales_brands` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主营品牌，逗号分隔的字典值',
  `biz_permissions` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务权限，逗号分隔的字典值(can_stock_in,can_sell,can_stock_out,can_settle)',
  `org_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构类型，字典值(group,single_store,secondary_network)',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_org_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNQ_NAME` (`org_name`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织架构表';

-- ----------------------------
-- Table structure for biz_org_member
-- ----------------------------
DROP TABLE IF EXISTS `biz_org_member`;
CREATE TABLE `biz_org_member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` bigint NOT NULL COMMENT '机构ID，关联biz_organizations.id',
  `agent_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工ID/工号',
  `agent_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工姓名',
  `position` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职务',
  `business_role` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务角色，字典值(sales_manager,warehouse_keeper,finance_specialist等)',
  `data_range` text COLLATE utf8mb4_unicode_ci COMMENT '数据查询范围，逗号分割的biz_org_id字符串',
  `data_range_names` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_agent_id` (`agent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织成员表';

-- ----------------------------
-- Table structure for crm_customer
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer`;
CREATE TABLE `crm_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '更新者ID',
  `customer_type` varchar(20) NOT NULL COMMENT '客户分类(individual个人/institutional机构)',
  `deal_status` varchar(20) NOT NULL COMMENT '成交状态(LEADS/CUSTOMER)',
  `owner_org_id` bigint NOT NULL COMMENT '客户归属机构ID',
  `owner_org_name` varchar(100) NOT NULL COMMENT '客户归属机构名称',
  `owner_seller_id` bigint NOT NULL COMMENT '客户归属销售顾问ID',
  `owner_seller_name` varchar(100) NOT NULL COMMENT '客户归属销售顾问',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称',
  `customer_id_code` varchar(100) NOT NULL COMMENT '客户识别码（身份证号码/统一社会信用代码）',
  `mobile` varchar(100) NOT NULL COMMENT '联系方式',
  `address` varchar(255) DEFAULT NULL COMMENT '联络地址',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_customer_info` (`owner_seller_id`,`customer_name`,`mobile`,`customer_type`) USING BTREE,
  KEY `idx_create_time` (`create_time`),
  KEY `idx_org_id` (`owner_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户信息表';

-- ----------------------------
-- Table structure for derivative_costs
-- ----------------------------
DROP TABLE IF EXISTS `derivative_costs`;
CREATE TABLE `derivative_costs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '销售订单号',
  `vin` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '车辆VIN码',
  `confirmed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '确认状态：0-未确认，1-已确认',
  `editor_org_id` bigint unsigned DEFAULT NULL COMMENT '经办机构ID',
  `editor_org_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办机构名称',
  `editor_id` bigint DEFAULT NULL COMMENT '经办人ID',
  `editor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人姓名',
  `notary_fee_income` bigint DEFAULT '0' COMMENT '公证费收入（分）',
  `notary_fee_cost` bigint DEFAULT '0' COMMENT '公证费成本（分）',
  `carefree_income` bigint DEFAULT '0' COMMENT '畅行无忧收入（分）',
  `carefree_cost` bigint DEFAULT '0' COMMENT '畅行无忧成本（分）',
  `extended_warranty_income` bigint DEFAULT '0' COMMENT '延保收入（分）',
  `extended_warranty_cost` bigint DEFAULT '0' COMMENT '延保成本（分）',
  `vps_income` bigint DEFAULT '0' COMMENT 'VPS收入（分）',
  `vps_cost` bigint DEFAULT '0' COMMENT 'VPS成本（分）',
  `pre_interest_income` bigint DEFAULT '0' COMMENT '预收利息收入（分）',
  `pre_interest_cost` bigint DEFAULT '0' COMMENT '预收利息成本（分）',
  `license_plate_fee_income` bigint DEFAULT '0' COMMENT '牌照费收入（分）',
  `license_plate_fee_cost` bigint DEFAULT '0' COMMENT '牌照费成本（分）',
  `temp_plate_fee_income` bigint DEFAULT '0' COMMENT '临牌费收入（分）',
  `temp_plate_fee_cost` bigint DEFAULT '0' COMMENT '临牌费成本（分）',
  `delivery_equipment_income` bigint DEFAULT '0' COMMENT '交车装备收入（分）',
  `delivery_equipment_cost` bigint DEFAULT '0' COMMENT '交车装备成本（分）',
  `other_income` bigint DEFAULT '0' COMMENT '其他收入（分）',
  `other_cost` bigint DEFAULT '0' COMMENT '其他成本（分）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_order_sn` (`order_sn`) USING BTREE COMMENT '订单号唯一索引',
  KEY `idx_vin` (`vin`) USING BTREE COMMENT 'VIN码索引',
  KEY `idx_confirmed` (`confirmed`) USING BTREE COMMENT '确认状态索引',
  KEY `idx_editor_org` (`editor_org_id`) USING BTREE COMMENT '经办机构索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='衍生费用管理表';

-- ----------------------------
-- Table structure for financial_accounts_setting
-- ----------------------------
DROP TABLE IF EXISTS `financial_accounts_setting`;
CREATE TABLE `financial_accounts_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键(BIGINT)',
  `usable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '账户启用状态',
  `owner_org_id` bigint NOT NULL DEFAULT '0' COMMENT '归属机构ID',
  `abbr` varchar(100) NOT NULL COMMENT '账户简称',
  `receivable` tinyint NOT NULL DEFAULT '1' COMMENT '可收款(0否1是)',
  `payable` tinyint NOT NULL DEFAULT '0' COMMENT '可付款(0否1是)',
  `init_amount` bigint NOT NULL DEFAULT '0' COMMENT '期初金额(分)',
  `remark` varchar(255) DEFAULT NULL COMMENT '账户备注',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '修改人ID',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_org_id` (`owner_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务账户设置表';

-- ----------------------------
-- Table structure for financial_fee_subject_setting
-- ----------------------------
DROP TABLE IF EXISTS `financial_fee_subject_setting`;
CREATE TABLE `financial_fee_subject_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键(BIGINT)',
  `subject_category` varchar(50) NOT NULL COMMENT '科目分类',
  `subject_name` varchar(100) NOT NULL COMMENT '科目名称',
  `default_amount` bigint NOT NULL DEFAULT '0' COMMENT '默认金额(分)',
  `remark` varchar(255) DEFAULT NULL COMMENT '科目备注',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '修改人ID',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `receivable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '可收入',
  `payable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '可支出',
  `usable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_subject` (`subject_category`,`subject_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2047 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务费用科目设置表';

-- ----------------------------
-- Table structure for financial_payable_bill
-- ----------------------------
DROP TABLE IF EXISTS `financial_payable_bill`;
CREATE TABLE `financial_payable_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `payable_org_id` bigint DEFAULT NULL,
  `payable_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `fee_id` bigint NOT NULL COMMENT '费用科目id',
  `fee_target` varchar(255) NOT NULL COMMENT '费用对象（银行/客户）',
  `fee_amount` int NOT NULL COMMENT '应付金额（分）',
  `payable_summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应付摘要',
  `bill_status` varchar(20) NOT NULL DEFAULT 'NOT_CONFIRM' COMMENT '对账确认（0-否/1-是）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `editor_id` bigint DEFAULT NULL COMMENT '最后编辑人ID',
  `source_table` varchar(255) DEFAULT NULL,
  `source_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fee_id` (`fee_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_org_id` (`payable_org_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务-应收账款对账单';

-- ----------------------------
-- Table structure for financial_payment_bill
-- ----------------------------
DROP TABLE IF EXISTS `financial_payment_bill`;
CREATE TABLE `financial_payment_bill` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `payment_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '付款单号（唯一键）',
  `payment_org_id` bigint NOT NULL COMMENT '付款机构id（非空）',
  `payment_time` datetime NOT NULL COMMENT '付款时间（非空）',
  `payment_amount` bigint NOT NULL COMMENT '付款金额（分，非空）',
  `biz_no` varchar(64) DEFAULT NULL COMMENT '业务流水号',
  `payment_summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '付款摘要',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（随数据更新）',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建人id（默认0）',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '修改人id（默认0）',
  `account_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_recept_sn` (`payment_sn`),
  KEY `idx_recept_time` (`payment_time`),
  KEY `idx_creator_id` (`creator_id`) USING BTREE,
  KEY `idx_dept_id` (`payment_org_id`) USING BTREE,
  KEY `idx_account_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收款单主表';

-- ----------------------------
-- Table structure for financial_payment_item
-- ----------------------------
DROP TABLE IF EXISTS `financial_payment_item`;
CREATE TABLE `financial_payment_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `payment_id` bigint NOT NULL COMMENT '所属付款单id（非空）',
  `fee_id` bigint NOT NULL COMMENT '科目id（非空）',
  `fee_name` varchar(50) NOT NULL COMMENT '科目名称（非空）',
  `fee_amount` bigint NOT NULL COMMENT '付款金额（分，非空）',
  `fee_summary` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '付款摘要（非空）',
  `payable_id` bigint DEFAULT NULL COMMENT '应付账款id',
  PRIMARY KEY (`id`),
  KEY `idx_recept_id` (`payment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收款单明细表';

-- ----------------------------
-- Table structure for financial_receivable_bill
-- ----------------------------
DROP TABLE IF EXISTS `financial_receivable_bill`;
CREATE TABLE `financial_receivable_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `receivable_org_id` bigint NOT NULL,
  `fee_id` bigint NOT NULL COMMENT '费用科目id',
  `fee_target` varchar(255) NOT NULL COMMENT '费用对象（银行/客户）',
  `fee_amount` int NOT NULL COMMENT '应收金额（分）',
  `receivable_summary` varchar(255) DEFAULT NULL COMMENT '应收摘要',
  `bill_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NOT_CONFIRM' COMMENT '对账状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `editor_id` bigint DEFAULT NULL COMMENT '最后编辑人ID',
  `receivable_org_name` varchar(255) DEFAULT NULL,
  `source_table` varchar(255) DEFAULT NULL COMMENT '应收来源表',
  `source_id` bigint DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fee_id` (`fee_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_org_id` (`receivable_org_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务-应收账款对账单';

-- ----------------------------
-- Table structure for financial_recept_bill
-- ----------------------------
DROP TABLE IF EXISTS `financial_recept_bill`;
CREATE TABLE `financial_recept_bill` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `account_id` bigint NOT NULL COMMENT '收款账户ID',
  `recept_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收款单号（唯一键）',
  `recept_org_id` bigint NOT NULL COMMENT '收款机构id（非空）',
  `recept_time` datetime NOT NULL COMMENT '收款时间（非空）',
  `recept_amount` bigint NOT NULL COMMENT '收款金额（分，非空）',
  `biz_no` varchar(64) DEFAULT NULL COMMENT '业务流水号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（随数据更新）',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建人id（默认0）',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '修改人id（默认0）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_recept_sn` (`recept_sn`),
  KEY `idx_recept_time` (`recept_time`),
  KEY `idx_creator_id` (`creator_id`) USING BTREE,
  KEY `idx_dept_id` (`recept_org_id`) USING BTREE,
  KEY `idx_account_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收款单主表';

-- ----------------------------
-- Table structure for financial_recept_item
-- ----------------------------
DROP TABLE IF EXISTS `financial_recept_item`;
CREATE TABLE `financial_recept_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `recept_id` bigint NOT NULL COMMENT '所属收款单id（非空）',
  `fee_id` bigint NOT NULL COMMENT '科目id（非空）',
  `fee_name` varchar(50) NOT NULL COMMENT '科目名称（非空）',
  `fee_amount` bigint NOT NULL COMMENT '收款金额（分，非空）',
  `fee_summary` varchar(100) NOT NULL COMMENT '收款摘要（非空）',
  `receivable_id` bigint DEFAULT NULL COMMENT '应收账款id',
  PRIMARY KEY (`id`),
  KEY `idx_recept_id` (`recept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=175 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收款单明细表';

-- ----------------------------
-- Table structure for gift_stock
-- ----------------------------
DROP TABLE IF EXISTS `gift_stock`;
CREATE TABLE `gift_stock` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `category` varchar(20) NOT NULL COMMENT '赠品类型（商品/服务）',
  `name` varchar(100) NOT NULL COMMENT '赠品名称',
  `spec` varchar(50) NOT NULL COMMENT '赠品规格',
  `unit` varchar(20) NOT NULL COMMENT '库存单位',
  `quantity` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `price` int NOT NULL DEFAULT '0' COMMENT '库存单价',
  `amount` bigint NOT NULL DEFAULT '0' COMMENT '库存金额',
  `remark` varchar(255) DEFAULT NULL COMMENT '库存备注',
  `stock_org_id` bigint NOT NULL COMMENT '存储机构id',
  `creator_id` bigint NOT NULL COMMENT '创建人id',
  `editor_id` bigint NOT NULL COMMENT '更新人id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_stock_org_id` (`stock_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='赠品库存表';

-- ----------------------------
-- Table structure for gift_stock_copy1
-- ----------------------------
DROP TABLE IF EXISTS `gift_stock_copy1`;
CREATE TABLE `gift_stock_copy1` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `category` varchar(20) NOT NULL COMMENT '赠品类型（商品/服务）',
  `name` varchar(100) NOT NULL COMMENT '赠品名称',
  `spec` varchar(50) NOT NULL COMMENT '赠品规格',
  `unit` varchar(20) NOT NULL COMMENT '库存单位',
  `quantity` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `price` int NOT NULL DEFAULT '0' COMMENT '库存单价',
  `amount` bigint NOT NULL DEFAULT '0' COMMENT '库存金额',
  `remark` varchar(255) DEFAULT NULL COMMENT '库存备注',
  `stock_org_id` bigint NOT NULL COMMENT '存储机构id',
  `creator_id` bigint NOT NULL COMMENT '创建人id',
  `editor_id` bigint NOT NULL COMMENT '更新人id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_stock_org_id` (`stock_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='赠品库存表';

-- ----------------------------
-- Table structure for gift_stock_outbound_record
-- ----------------------------
DROP TABLE IF EXISTS `gift_stock_outbound_record`;
CREATE TABLE `gift_stock_outbound_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `gift_id` bigint NOT NULL COMMENT '礼品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_sn` varchar(100) NOT NULL COMMENT '订单编号',
  `gift_name` varchar(255) NOT NULL COMMENT '礼品名称',
  `gift_spec` varchar(255) NOT NULL COMMENT '礼品规格',
  `quantity` int NOT NULL COMMENT '出库数量',
  `creator_id` bigint NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `outbound_org_id` bigint NOT NULL COMMENT '出库组织ID',
  `outbound_org_name` varchar(255) NOT NULL COMMENT '出库组织名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `outbound_amount` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='礼品出库记录表';

-- ----------------------------
-- Table structure for jwd_roles
-- ----------------------------
DROP TABLE IF EXISTS `jwd_roles`;
CREATE TABLE `jwd_roles` (
  `id` int NOT NULL,
  `roleName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `roleCode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `roleType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `useType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `authRange` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `userRangeType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `roleRangeType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `sysRange` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `sysRangeList` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for sales_price_limit_rule
-- ----------------------------
DROP TABLE IF EXISTS `sales_price_limit_rule`;
CREATE TABLE `sales_price_limit_rule` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `rule_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',
  `effective_time` bigint NOT NULL COMMENT '生效时间（毫秒时间戳转换）',
  `expiry_time` bigint NOT NULL COMMENT '失效时间',
  `rule_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则类型',
  `price_limit` bigint unsigned NOT NULL COMMENT '价格限制（单位：元）',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `effective_org` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生效组织ID列表（逗号分隔）',
  `effective_org_names` longtext COLLATE utf8mb4_unicode_ci,
  `rule_details` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则详情（JSON格式存储）',
  `creator_id` bigint unsigned NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规则表';

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop` (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `parent_id` int DEFAULT NULL,
  `biz_type` int DEFAULT NULL COMMENT '1-新车销售，2-售后网点,3-二手车,5-交付中心,6-配件库房，7-新车库房',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for staff
-- ----------------------------
DROP TABLE IF EXISTS `staff`;
CREATE TABLE `staff` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staffName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `nation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `houseType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `shopId` int DEFAULT NULL,
  `shopName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `bankName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `bankCard` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `selfCardName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `selfCard` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `idNumber` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `idCardAddress` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `fundShopId` int DEFAULT NULL,
  `fundShopName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `entryDate` date DEFAULT NULL,
  `regularDate` date DEFAULT NULL,
  `leaveDate` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=735 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for stock_outbound_bill
-- ----------------------------
DROP TABLE IF EXISTS `stock_outbound_bill`;
CREATE TABLE `stock_outbound_bill` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增数据主键',
  `order_id` int unsigned NOT NULL COMMENT '销售订单ID',
  `outbound_org_id` int unsigned NOT NULL COMMENT '出库单位ID',
  `outbound_agent_id` int DEFAULT NULL COMMENT '出库人ID',
  `outbound_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '出库时间',
  `sku_id` int unsigned NOT NULL COMMENT '出库SKU_ID',
  `vin` varchar(17) DEFAULT NULL COMMENT '车架号（VIN）',
  `outbound_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '待出库' COMMENT '出库状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order` (`order_id`) USING BTREE,
  KEY `idx_org` (`outbound_org_id`),
  KEY `idx_sku` (`sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='库存出库单';

-- ----------------------------
-- Table structure for system_department
-- ----------------------------
DROP TABLE IF EXISTS `system_department`;
CREATE TABLE `system_department` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '部门名称',
  `dept_abbr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门简称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级部门ID',
  `order_num` int DEFAULT '0' COMMENT '排序',
  `im_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'IM供应商（企微/飞书/钉钉)',
  `im_dept_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'IM机构ID',
  `biz_type` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNQ_IM_DEPT` (`im_dept_id`,`im_provider`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1088 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统部门';

-- ----------------------------
-- Table structure for system_menu
-- ----------------------------
DROP TABLE IF EXISTS `system_menu`;
CREATE TABLE `system_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `menu_path` varchar(100) DEFAULT NULL,
  `view_path` varchar(255) DEFAULT NULL,
  `menu_label` varchar(20) DEFAULT NULL,
  `menu_icon` varchar(100) DEFAULT NULL,
  `menu_order` int DEFAULT NULL,
  `parent_id` bigint DEFAULT NULL,
  `visible` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_option
-- ----------------------------
DROP TABLE IF EXISTS `system_option`;
CREATE TABLE `system_option` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `option_group` varchar(64) DEFAULT NULL,
  `option_group_name` varchar(255) DEFAULT NULL,
  `option_group_type` varchar(255) DEFAULT NULL,
  `option_value` varchar(64) DEFAULT NULL,
  `option_label` varchar(64) DEFAULT NULL,
  `option_order` int DEFAULT NULL,
  `option_comment` varchar(256) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `option_org_range` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_OPTION_GROUP_VALUE` (`option_group`,`option_value`,`option_org_range`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_role
-- ----------------------------
DROP TABLE IF EXISTS `system_role`;
CREATE TABLE `system_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_code` varchar(255) DEFAULT NULL,
  `role_name` varchar(50) DEFAULT NULL,
  `data_scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_role_acl
-- ----------------------------
DROP TABLE IF EXISTS `system_role_acl`;
CREATE TABLE `system_role_acl` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint DEFAULT NULL,
  `request_path` varchar(255) DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_comment` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_role_menus
-- ----------------------------
DROP TABLE IF EXISTS `system_role_menus`;
CREATE TABLE `system_role_menus` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `menu_id` bigint DEFAULT NULL,
  `role_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2812 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_user
-- ----------------------------
DROP TABLE IF EXISTS `system_user`;
CREATE TABLE `system_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `passwd` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `nickname` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `access_token` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `disabled` tinyint(1) DEFAULT '0' COMMENT '是否停用（默认0、未停用，1、已停用）',
  `department_id` bigint DEFAULT NULL,
  `wecom_corp_id` varchar(255) DEFAULT '0',
  `work_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `wecom_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `position` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `system_user_username_uindex` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8952 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for system_user_roles
-- ----------------------------
DROP TABLE IF EXISTS `system_user_roles`;
CREATE TABLE `system_user_roles` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `role_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNQ_U_R` (`user_id`,`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=440 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for used_vehicle_inventory
-- ----------------------------
DROP TABLE IF EXISTS `used_vehicle_inventory`;
CREATE TABLE `used_vehicle_inventory` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `vehicle_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆类型(必填)',
  `color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '颜色(选填)',
  `vehicle_id` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车牌号(必填)',
  `brand` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '品牌',
  `series` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '型号',
  `vin` varchar(17) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车架号(必填,17位)',
  `registration_date` date DEFAULT NULL COMMENT '注册日期',
  `mileage` decimal(10,2) DEFAULT NULL COMMENT '行驶里程(公里)',
  `check_deadline` date DEFAULT NULL COMMENT '审车到期日期',
  `insurance_deadline` date DEFAULT NULL COMMENT '保险到期时间',
  `vehicle_source` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '库存来源(必填): 置换/收购',
  `inbound_org_id` bigint NOT NULL COMMENT '收购单位(必填)',
  `inbound_payment_method` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '付款方式(必填)',
  `stock_org_id` bigint NOT NULL COMMENT '车辆存放单位(必填)',
  `stock_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '库存状态(必填): 在库/已售',
  `inbound_date` date DEFAULT NULL COMMENT '入库日期(yyyy-MM-dd)',
  `inbound_customer_id` bigint DEFAULT NULL COMMENT '卖方客户标识',
  `ref_order_id` bigint DEFAULT NULL COMMENT '关联新车订单id(选填)',
  `inbound_amount` bigint DEFAULT NULL COMMENT '入库价格(分)',
  `inbound_agent_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '入库专员',
  `leads_source` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '线下' COMMENT '收车线索来源: 线上/线下(默认线下)',
  `leads_provider_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '销售线索提供者姓名',
  `leads_provider_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '销售线索提供者联系方式',
  `outbound_org_id` bigint DEFAULT NULL COMMENT '出库公司',
  `outbound_payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收款方式',
  `outbound_date` date DEFAULT NULL COMMENT '出库日期(yyyy-MM-dd)',
  `outbound_amount` bigint DEFAULT NULL COMMENT '出库价格(分)',
  `outbound_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '出库方式: 零售/批发',
  `outbound_agent_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '出库人员',
  `outbound_customer_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '买方客户标识',
  `gross_profit` bigint GENERATED ALWAYS AS ((`outbound_amount` - `inbound_amount`)) STORED COMMENT '毛利润(出库金额减入库金额)',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `commission_ratio` double(255,0) DEFAULT NULL COMMENT '佣金比例',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vehicle_id` (`vehicle_id`),
  UNIQUE KEY `uk_vin` (`vin`),
  KEY `idx_stock_org_id` (`stock_org_id`),
  KEY `idx_inbound_date` (`inbound_date`),
  KEY `idx_outbound_date` (`outbound_date`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='二手车库存表';

-- ----------------------------
-- Table structure for vehicle_deposit_order
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_deposit_order`;
CREATE TABLE `vehicle_deposit_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `order_sn` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `seller_agent_id` bigint NOT NULL COMMENT '销售顾问ID',
  `seller_org_id` bigint NOT NULL COMMENT '销售机构ID',
  `deposit_status` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '定金状态(如：待支付/已支付/已退款)',
  `deposit_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '定金金额(单位：分)',
  `deposit_remark` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定金备注',
  `creator_id` bigint NOT NULL COMMENT '创建人ID',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '最后修改人ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deposit_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_seller_agent` (`seller_agent_id`),
  KEY `idx_seller_org` (`seller_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='车辆定金订单表';

-- ----------------------------
-- Table structure for vehicle_insurance
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_insurance`;
CREATE TABLE `vehicle_insurance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `vin` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车架号',
  `insurance_provider` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保险公司',
  `ci_fee` int NOT NULL DEFAULT '0' COMMENT '交强险保费，单位：分',
  `ci_rebate` int NOT NULL DEFAULT '0' COMMENT '交强险返利，单位：分',
  `bi_fee` int NOT NULL DEFAULT '0' COMMENT '商业险保费，单位：分',
  `bi_rebate` int NOT NULL DEFAULT '0' COMMENT '商业险返利，单位：分',
  `oi_fee` int NOT NULL DEFAULT '0' COMMENT '非车险保费，单位：分',
  `oi_rebate` int NOT NULL DEFAULT '0' COMMENT '非车险返利，单位：分',
  `rebate_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT '返利状态：PENDING-待返利，PROCESSING-返利中，COMPLETED-已返利，FAILED-返利失败',
  `biz_org_id` bigint NOT NULL COMMENT '经办机构ID',
  `biz_agent_id` bigint DEFAULT NULL COMMENT '经办员工ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vehicle_order_id` (`vin`),
  KEY `idx_biz_org_id` (`biz_org_id`),
  KEY `idx_biz_agent_id` (`biz_agent_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车险表';

-- ----------------------------
-- Table structure for vehicle_order
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_order`;
CREATE TABLE `vehicle_order` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `order_type` varchar(255) NOT NULL DEFAULT 'normal' COMMENT '订单类型',
  `order_sn` varchar(64) NOT NULL COMMENT '订单编号',
  `order_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单日期',
  `customer_id` int unsigned NOT NULL COMMENT '客户ID',
  `sales_org_id` int unsigned NOT NULL COMMENT '销售单位ID',
  `sales_agent_id` int unsigned NOT NULL COMMENT '销售顾问ID',
  `sales_leader_id` int unsigned DEFAULT NULL COMMENT '销售主管ID',
  `order_status` enum('pending','confirmed','delivered','canceled','archived') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `sku_id` int unsigned NOT NULL COMMENT 'vehicle_sku.id',
  `delivery_date` date NOT NULL COMMENT '交付日期',
  `delivery_org_id` int unsigned NOT NULL COMMENT '交付单位ID',
  `sb_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '启票总价（分）',
  `discount_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '优惠金额（分）',
  `discount_deductible` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠金额转车款',
  `sales_cost_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '销售费用（分）',
  `deal_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '成交总价（分）',
  `deal_amount_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '成交总价大写',
  `payment_method` enum('FULL','LOAN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'FULL' COMMENT '付款方式',
  `loan_channel` enum('车贷通担保','建行','长安金融','上汽金融','其他') DEFAULT NULL COMMENT '贷款渠道',
  `loan_months` int DEFAULT NULL COMMENT '贷款期数',
  `loan_amount` int unsigned DEFAULT '0' COMMENT '贷款金额（分）',
  `loan_initial_amount` int unsigned DEFAULT '0' COMMENT '首付金额（分）',
  `loan_initial_ratio` decimal(5,2) GENERATED ALWAYS AS (if((`loan_amount` > 0),(`loan_initial_amount` / `loan_amount`),0)) STORED COMMENT '首付比例',
  `loan_rebate_payable_deductible` tinyint DEFAULT NULL COMMENT '返利转车款',
  `used_vehicle_id` varchar(20) DEFAULT NULL COMMENT '二手车车牌号',
  `used_vehicle_vin` varchar(17) DEFAULT NULL COMMENT '二手车VIN',
  `used_vehicle_amount` int unsigned DEFAULT '0' COMMENT '置换金额（分）',
  `used_vehicle_deductible_amount` int unsigned DEFAULT '0' COMMENT '置换补贴（分）',
  `used_vehicle_discount_receivable_amount` int DEFAULT '0' COMMENT '收厂家补贴金额',
  `used_vehicle_discount_payable_amount` int DEFAULT '0' COMMENT '付客户补贴金额',
  `used_vehicle_discount_payable_deductible` tinyint DEFAULT NULL COMMENT '补贴转车款',
  `used_vehicle_brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '二手车品牌',
  `used_vehicle_model` varchar(50) DEFAULT NULL COMMENT '二手车车型',
  `used_vehicle_color` varchar(20) DEFAULT NULL COMMENT '二手车颜色',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint NOT NULL COMMENT '创建人ID',
  `editor_id` bigint NOT NULL COMMENT '最后编辑人ID',
  `deposit_type` varchar(255) DEFAULT NULL COMMENT '定金类型：线上/线下',
  `deposit_amount` int DEFAULT NULL COMMENT '定金金额',
  `deposit_deductible` tinyint DEFAULT NULL COMMENT '定金转车款',
  `deposit_usable` varchar(255) DEFAULT 'NO' COMMENT '定金状态',
  `carefree_income` bigint DEFAULT NULL,
  `delivery_equipment` bigint DEFAULT NULL,
  `extended_warranty_income` bigint DEFAULT NULL,
  `has_derivative_income` varchar(20) DEFAULT NULL,
  `has_gift_items` varchar(20) DEFAULT NULL,
  `has_insurance` varchar(20) DEFAULT NULL,
  `license_plate_fee` bigint DEFAULT '0',
  `notary_fee` bigint DEFAULT '0',
  `pre_interest` bigint DEFAULT '0',
  `temp_plate_fee` bigint DEFAULT '0',
  `vps_income` bigint DEFAULT '0',
  `other_income` bigint DEFAULT '0',
  `loan_fee` bigint DEFAULT '0' COMMENT '分期服务费(分)',
  `loan_rebate_receivable_amount` bigint DEFAULT '0' COMMENT '应收-机构-分期返利(分)',
  `loan_rebate_payable_amount` bigint DEFAULT '0' COMMENT '应付-客户-分期返利(分)',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '订单备注',
  `exclusive_discount_payable_amount` bigint DEFAULT '0' COMMENT '专项优惠金额',
  `exclusive_discount_receivable_amount` varchar(255) DEFAULT NULL,
  `exclusive_discount_type` varchar(255) DEFAULT NULL COMMENT '专项优惠类型',
  `exclusive_discount_payable_deductible` tinyint DEFAULT NULL COMMENT '专项优惠抵车款',
  `exclusive_discount_remark` varchar(255) DEFAULT NULL COMMENT '专项优惠说明',
  `sales_amount` int DEFAULT NULL,
  `invoice_amount` varchar(255) DEFAULT NULL,
  `gift_items` longtext,
  `insurance_org_id` bigint DEFAULT NULL COMMENT '保险经办机构',
  `sales_store_type` varchar(255) DEFAULT NULL COMMENT '销售地类型',
  `has_exclusive_discount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '是否存在专项优惠',
  `recept_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'NO' COMMENT '收款状态YES-NO',
  `sales_store_remark` varchar(255) DEFAULT NULL COMMENT '二网备注',
  `sales_store_name` varchar(255) DEFAULT NULL COMMENT '直营店名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_order_sn` (`order_sn`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_sales_org` (`sales_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售订单主表';

-- ----------------------------
-- Table structure for vehicle_sku
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_sku`;
CREATE TABLE `vehicle_sku` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `brand` varchar(255) DEFAULT NULL COMMENT '品牌',
  `series` varchar(255) DEFAULT NULL COMMENT '车系',
  `model_code` varchar(255) DEFAULT NULL COMMENT '车型代码',
  `model_name` varchar(255) DEFAULT NULL COMMENT '车型名称',
  `config_code` varchar(255) DEFAULT NULL COMMENT '配置代码',
  `config_name` varchar(255) DEFAULT NULL COMMENT '配置名称',
  `sku_id` varchar(255) DEFAULT NULL COMMENT '物料代码',
  `color_code` varchar(255) DEFAULT NULL COMMENT '颜色代码=物料代码第3段',
  `sb_price` int DEFAULT NULL COMMENT '启票单价',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_id` (`sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=464 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车辆SKU信息表';

-- ----------------------------
-- Table structure for vehicle_start_bill_item
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_start_bill_item`;
CREATE TABLE `vehicle_start_bill_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_code` varchar(32) NOT NULL COMMENT '批次代码，关联启票记录',
  `erp_order_date` varchar(32) DEFAULT NULL COMMENT 'ERP订单日期',
  `erp_order_no` varchar(64) DEFAULT NULL COMMENT 'ERP订单号',
  `order_type` varchar(32) DEFAULT NULL COMMENT '订单类型',
  `vin` varchar(64) DEFAULT NULL COMMENT 'VIN码',
  `report_type` varchar(32) DEFAULT NULL COMMENT '提报类型',
  `business_unit` varchar(64) DEFAULT NULL COMMENT '事业部',
  `battle_area` varchar(64) DEFAULT NULL COMMENT '战区',
  `sdu` varchar(64) DEFAULT NULL COMMENT 'SDU',
  `sdu_member` varchar(64) DEFAULT NULL COMMENT 'SDU队员',
  `province` varchar(32) DEFAULT NULL COMMENT '省份',
  `city` varchar(32) DEFAULT NULL COMMENT '城市',
  `investment_entity_code` varchar(64) DEFAULT NULL COMMENT '同一投资主体编码',
  `investment_entity` varchar(128) DEFAULT NULL COMMENT '同一投资主体',
  `service_center` varchar(128) DEFAULT NULL COMMENT '服务中心',
  `invoice_org_id` bigint DEFAULT NULL COMMENT '开票组织编码',
  `invoice_org_name` varchar(128) DEFAULT NULL COMMENT '开票组织名称',
  `purchase_org_id` bigint DEFAULT NULL COMMENT '采购组织ID',
  `purchase_org_name` varchar(128) DEFAULT NULL COMMENT '采购组织名称',
  `receiving_org_id` bigint DEFAULT NULL COMMENT '收车组织编码',
  `receiving_org_name` varchar(128) DEFAULT NULL COMMENT '收车组织名称',
  `shipping_address` varchar(255) DEFAULT NULL COMMENT '发运地址',
  `vehicle_category` varchar(64) DEFAULT NULL COMMENT '车别',
  `vehicle_series` varchar(64) DEFAULT NULL COMMENT '车系',
  `vehicle_model_code` varchar(64) DEFAULT NULL COMMENT '车型编号',
  `vehicle_model_name` varchar(128) DEFAULT NULL COMMENT '车型名称',
  `material_code` varchar(64) DEFAULT NULL COMMENT '物料代码',
  `material_name` varchar(128) DEFAULT NULL COMMENT '物料名称',
  `start_bill_price` decimal(12,2) DEFAULT NULL COMMENT '启票价格（元）',
  `start_bill_price_cent` bigint DEFAULT NULL COMMENT '启票价格（分）',
  `payment_remark` varchar(255) DEFAULT NULL COMMENT '付款备注',
  `fund_type` varchar(32) DEFAULT NULL COMMENT '资金类型',
  `shipping_application_date` varchar(32) DEFAULT NULL COMMENT '发运申请日期',
  `shipping_application_no` varchar(64) DEFAULT NULL COMMENT '发运申请单号',
  `batch_sale_invoice_no` varchar(64) DEFAULT NULL COMMENT '批售发票编号',
  `batch_sale_invoice_date` varchar(32) DEFAULT NULL COMMENT '批售发票日期',
  `creator_id` bigint DEFAULT NULL,
  `creator_org_id` bigint DEFAULT NULL,
  `editor_id` bigint DEFAULT NULL,
  `editor_org_id` bigint DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_vin` (`vin`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8866 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='启票项表';

-- ----------------------------
-- Table structure for vehicle_start_bill_record
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_start_bill_record`;
CREATE TABLE `vehicle_start_bill_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_code` varchar(32) NOT NULL COMMENT '批次代码，格式：QP$yyyyMMdd$SN',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建者姓名',
  `org_id` bigint DEFAULT NULL COMMENT '创建者机构ID',
  `org_name` varchar(128) DEFAULT NULL COMMENT '创建者机构名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `item_count` int DEFAULT '0' COMMENT '启票项数量',
  `total_price_cent` bigint DEFAULT '0' COMMENT '启票总价格（分）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_code` (`batch_code`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='启票记录表';

-- ----------------------------
-- Table structure for vehicle_stock
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_stock`;
CREATE TABLE `vehicle_stock` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '更新者ID',
  `sku_id` bigint NOT NULL DEFAULT '0' COMMENT 'SKU_ID',
  `vin` varchar(50) NOT NULL COMMENT '车辆VIN码',
  `stock_amount` bigint NOT NULL COMMENT '库存金额(单位:分)',
  `stock_status` varchar(20) NOT NULL COMMENT '库存状态(transiting在途/stocking在库/sold已售/returned已退)',
  `stock_days` int NOT NULL DEFAULT '0' COMMENT '库龄天数',
  `sb_org_id` bigint NOT NULL COMMENT '启票机构ID',
  `sb_org_name` varchar(100) NOT NULL COMMENT '启票机构名称',
  `owner_org_id` bigint NOT NULL COMMENT '采购机构ID',
  `owner_org_name` varchar(100) NOT NULL COMMENT '采购机构名称',
  `stock_org_id` bigint NOT NULL COMMENT '仓储机构ID',
  `stock_org_name` varchar(100) NOT NULL COMMENT '仓储机构名称',
  `trialing_begin_date` bigint DEFAULT NULL COMMENT '标记试乘试驾的时间',
  `trial_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'none' COMMENT '试乘试驾状态',
  `stock_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'none' COMMENT '现金车=CASH',
  `transfer_status` varchar(255) DEFAULT NULL COMMENT '调拨状态',
  `depreciation_amount` bigint DEFAULT '0',
  `manufacturer_support_amount` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vin` (`vin`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_stock_org_id` (`stock_org_id`),
  KEY `owner_org_id` (`owner_org_id`),
  KEY `idx_sku_id` (`sku_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8866 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车辆库存信息表';

-- ----------------------------
-- Table structure for vehicle_stock_transfer
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_stock_transfer`;
CREATE TABLE `vehicle_stock_transfer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `vin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车架号',
  `stock_id` bigint NOT NULL COMMENT '库存ID（业务主键）',
  `source_org_id` bigint DEFAULT NULL COMMENT '发起方机构id',
  `source_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发起方机构名称',
  `target_org_id` bigint DEFAULT NULL COMMENT '接收方机构id',
  `target_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '接收方机构名称',
  `transfer_type` varchar(50) DEFAULT NULL COMMENT '调拨类型（如：内部调拨、外部转移）',
  `transfer_reason` varchar(500) DEFAULT NULL COMMENT '调拨原因',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `receivable_ratio` double DEFAULT NULL COMMENT '应收款比例',
  `creator_agent_id` bigint DEFAULT NULL COMMENT '发起人ID',
  `creator_agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发起人名称',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（自动填充）',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（自动更新）',
  `transfer_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '调拨状态',
  `recept_agent_id` bigint DEFAULT NULL COMMENT '接收人ID',
  `recept_agent_name` varchar(255) DEFAULT NULL COMMENT '接收人名称',
  PRIMARY KEY (`id`),
  KEY `idx_stock_id` (`stock_id`),
  KEY `idx_creator` (`creator_agent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车辆库存调拨记录表';

-- ----------------------------
-- Table structure for vin_trace
-- ----------------------------
DROP TABLE IF EXISTS `vin_trace`;
CREATE TABLE `vin_trace` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vin` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `event_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `event_desc` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`id`),
  KEY `IDX_VIN` (`vin`)
) ENGINE=InnoDB AUTO_INCREMENT=5940 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for wecom_app
-- ----------------------------
DROP TABLE IF EXISTS `wecom_app`;
CREATE TABLE `wecom_app` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户的corp_id',
  `open_corp_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户的open_corp_id',
  `suite_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发模板id',
  `suite_secret` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发模板secret',
  `suite_ticket` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发ticket',
  `auth_code` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户授权码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_corp_id` (`corp_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;

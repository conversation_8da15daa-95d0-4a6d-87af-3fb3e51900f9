public class StatOutboundVO {

    // fd子查询字段
    @ExcelProperty(value = "收款时间", index = 0)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date receptTime;

    @ExcelProperty(value = "账户名称", index = 1)
    private String accountName;

    @ExcelProperty(value = "费用名称", index = 2)
    private String feeName;

    @ExcelProperty(value = "费用金额(元)", index = 3, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long feeAmount;

    @ExcelProperty(value = "订单ID", index = 4)
    @NumberFormat("#")
    private Long orderId;

    @ExcelProperty(value = "收款机构名称", index = 5)
    private String receptOrgName;

    // vehicle_order表字段
    @ExcelProperty(value = "订单编号", index = 6)
    private String orderSn;

    @ExcelProperty(value = "订单日期", index = 7)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    @ExcelProperty(value = "销售机构ID", index = 8)
    @NumberFormat("#")
    private Long salesOrgId;

    @ExcelProperty(value = "销售顾问ID", index = 9)
    @NumberFormat("#")
    private Long salesAgentId;

    @ExcelProperty(value = "启票总价(元)", index = 10, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long sbAmount;

    @ExcelProperty(value = "优惠金额(元)", index = 11, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long discountAmount;

    @ExcelProperty(value = "优惠金额转车款", index = 12, converter = BooleanConverter.class)
    @ColumnWidth(12)
    private Boolean discountDeductible;

    @ExcelProperty(value = "销售费用(元)", index = 13, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long salesCostAmount;

    @ExcelProperty(value = "成交总价(元)", index = 14, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dealAmount;

    @ExcelProperty(value = "付款方式", index = 15)
    private String paymentMethod;

    @ExcelProperty(value = "贷款渠道", index = 16)
    private String loanChannel;

    @ExcelProperty(value = "贷款期数", index = 17)
    private Integer loanMonths;

    @ExcelProperty(value = "贷款金额(元)", index = 18, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanAmount;

    @ExcelProperty(value = "首付金额(元)", index = 19, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanInitialAmount;

    @ExcelProperty(value = "首付比例", index = 20)
    @NumberFormat("0.00%")
    private Double loanInitialRatio;

    @ExcelProperty(value = "返利转车款", index = 21, converter = BooleanConverter.class)
    @ColumnWidth(12)
    private Boolean loanRebatePayableDeductible;

    @ExcelProperty(value = "二手车车牌号", index = 22)
    private String usedVehicleId;

    @ExcelProperty(value = "二手车VIN", index = 23)
    private String usedVehicleVin;

    @ExcelProperty(value = "置换金额(元)", index = 24, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long usedVehicleAmount;

    @ExcelProperty(value = "置换补贴(元)", index = 25, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long usedVehicleDeductibleAmount;

    @ExcelProperty(value = "收厂家补贴金额(元)", index = 26, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long usedVehicleDiscountReceivableAmount;

    @ExcelProperty(value = "付客户补贴金额(元)", index = 27, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long usedVehicleDiscountPayableAmount;

    @ExcelProperty(value = "补贴转车款", index = 28, converter = BooleanConverter.class)
    @ColumnWidth(12)
    private Boolean usedVehicleDiscountPayableDeductible;

    @ExcelProperty(value = "二手车品牌", index = 29)
    private String usedVehicleBrand;

    @ExcelProperty(value = "二手车车型", index = 30)
    private String usedVehicleModel;

    @ExcelProperty(value = "二手车颜色", index = 31)
    private String usedVehicleColor;

    @ExcelProperty(value = "定金转车款", index = 32, converter = BooleanConverter.class)
    @ColumnWidth(12)
    private Boolean depositDeductible;

    @ExcelProperty(value = "交车装备(元)", index = 33, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long deliveryEquipment;

    @ExcelProperty(value = "是否有赠品", index = 34, converter = FlagConverter.class)
    @ColumnWidth(12)
    private Flag hasGiftItems;

    @ExcelProperty(value = "是否有保险", index = 35, converter = FlagConverter.class)
    @ColumnWidth(12)
    private Flag hasInsurance;

    @ExcelProperty(value = "分期服务费(元)", index = 36, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanFee;

    @ExcelProperty(value = "应收分期返利(元)", index = 37, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanRebateReceivableAmount;

    @ExcelProperty(value = "应付分期返利(元)", index = 38, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanRebatePayableAmount;

    @ExcelProperty(value = "专项优惠金额(元)", index = 39, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long exclusiveDiscountPayableAmount;

    @ExcelProperty(value = "专项优惠应收金额(元)", index = 40, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long exclusiveDiscountReceivableAmount;

    @ExcelProperty(value = "专项优惠类型", index = 41)
    private String exclusiveDiscountType;

    @ExcelProperty(value = "专项优惠抵车款", index = 42, converter = BooleanConverter.class)
    @ColumnWidth(12)
    private Boolean exclusiveDiscountPayableDeductible;

    @ExcelProperty(value = "专项优惠说明", index = 43)
    private String exclusiveDiscountRemark;

    @ExcelProperty(value = "销售金额(元)", index = 44, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long salesAmount;

    @ExcelProperty(value = "发票金额(元)", index = 45, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long invoiceAmount;

    @ExcelProperty(value = "赠品项目", index = 46)
    private String giftItems;

    @ExcelProperty(value = "销售地类型", index = 47)
    private String salesStoreType;

    @ExcelProperty(value = "是否存在专项优惠", index = 48, converter = FlagConverter.class)
    @ColumnWidth(12)
    private Flag hasExclusiveDiscount;

    @ExcelProperty(value = "二网备注", index = 49)
    private String salesStoreRemark;

    @ExcelProperty(value = "直营店名称", index = 50)
    private String salesStoreName;

    // stock_outbound_bill表字段
    @ExcelProperty(value = "车架号VIN", index = 51)
    private String vin;

    @ExcelProperty(value = "出库机构ID", index = 52)
    @NumberFormat("#")
    private Long outboundOrgId;

    @ExcelProperty(value = "出库人ID", index = 53)
    @NumberFormat("#")
    private Long outboundAgentId;

    @ExcelProperty(value = "出库时间", index = 54)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date outboundTime;

    // vehicle_stock表字段
    @ExcelProperty(value = "库存金额(元)", index = 55, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long stockAmount;

    @ExcelProperty(value = "库龄天数", index = 56)
    private int stockDays;

    @ExcelProperty(value = "启票机构名称", index = 57)
    private String sbOrgName;

    @ExcelProperty(value = "仓储机构名称", index = 58)
    private String stockOrgName;

    @ExcelProperty(value = "试乘试驾状态", index = 59)
    private String trialStatus;

    @ExcelProperty(value = "库存类型", index = 60)
    private String stockType;

    @ExcelProperty(value = "折旧金额(元)", index = 61, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long depreciationAmount;

    @ExcelProperty(value = "厂家支持金额(元)", index = 62, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long manufacturerSupportAmount;

    // derivative_costs表字段
    @ExcelProperty(value = "公证费收入(元)", index = 63, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long notaryFeeIncome;

    @ExcelProperty(value = "公证费成本(元)", index = 64, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long notaryFeeCost;

    @ExcelProperty(value = "畅行无忧收入(元)", index = 65, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dcCarefreeIncome;

    @ExcelProperty(value = "畅行无忧成本(元)", index = 66, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long carefreeCost;

    @ExcelProperty(value = "延保收入(元)", index = 67, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dcExtendedWarrantyIncome;

    @ExcelProperty(value = "延保成本(元)", index = 68, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long extendedWarrantyCost;

    @ExcelProperty(value = "VPS收入(元)", index = 69, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dcVpsIncome;

    @ExcelProperty(value = "VPS成本(元)", index = 70, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long vpsCost;

    @ExcelProperty(value = "预收利息收入(元)", index = 71, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long preInterestIncome;

    @ExcelProperty(value = "预收利息成本(元)", index = 72, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long preInterestCost;

    @ExcelProperty(value = "牌照费收入(元)", index = 73, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long licensePlateFeeIncome;

    @ExcelProperty(value = "牌照费成本(元)", index = 74, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long licensePlateFeeCost;

    @ExcelProperty(value = "临牌费收入(元)", index = 75, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long tempPlateFeeIncome;

    @ExcelProperty(value = "临牌费成本(元)", index = 76, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long tempPlateFeeCost;

    @ExcelProperty(value = "交车装备收入(元)", index = 77, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long deliveryEquipmentIncome;

    @ExcelProperty(value = "交车装备成本(元)", index = 78, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long deliveryEquipmentCost;

    @ExcelProperty(value = "其他收入(元)", index = 79, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dcOtherIncome;

    @ExcelProperty(value = "其他成本(元)", index = 80, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long otherCost;

    // vehicle_sku表字段
    @ExcelProperty(value = "品牌", index = 81)
    private String brand;

    @ExcelProperty(value = "车系", index = 82)
    private String series;

    @ExcelProperty(value = "车型代码", index = 83)
    private String modelCode;

    @ExcelProperty(value = "车型名称", index = 84)
    private String modelName;

    @ExcelProperty(value = "配置代码", index = 85)
    private String configCode;

    @ExcelProperty(value = "配置名称", index = 86)
    private String configName;

    @ExcelProperty(value = "颜色代码", index = 87)
    private String colorCode;

    @ExcelProperty(value = "启票单价(元)", index = 88, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long sbPrice;

    // vehicle_insurance表字段
    @ExcelProperty(value = "保险公司", index = 89)
    private String insuranceProvider;

    @ExcelProperty(value = "交强险保费(元)", index = 90, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long ciFee;

    @ExcelProperty(value = "交强险返利(元)", index = 91, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long ciRebate;

    @ExcelProperty(value = "商业险保费(元)", index = 92, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long biFee;

    @ExcelProperty(value = "商业险返利(元)", index = 93, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long biRebate;

    @ExcelProperty(value = "非车险保费(元)", index = 94, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long oiFee;

    @ExcelProperty(value = "非车险返利(元)", index = 95, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long oiRebate;

    @ExcelProperty(value = "返利状态", index = 96)
    private String rebateStatus;

    // crm_customer表字段
    @ExcelProperty(value = "客户分类", index = 97)
    private String customerType;

    @ExcelProperty(value = "客户归属机构名称", index = 98)
    private String cusOwnerOrgName;

    @ExcelProperty(value = "客户归属销售顾问", index = 99)
    private String ownerSellerName;

    @ExcelProperty(value = "客户名称", index = 100)
    private String customerName;

    @ExcelProperty(value = "联系方式", index = 101)
    private String mobile;

}
import { doGet, doPost, doPut, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'

/**
 * 出库单 API 服务 - RESTful风格
 */
export const outboundApi = {
  /**
   * 获取出库单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.outboundOrgId] - 出库单位ID
   * @param {String} [params.beginDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.orderSn] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.outboundStatus] - 出库状态
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回出库单列表的 Promise
   */
  getOutboundList(params) {
    return doGet('/vehicle/outbound/query', params)
  },

  /**
   * 获取出库单详情
   * @param {Number} id - 出库单 ID
   * @returns {Promise} 返回出库单详情的 Promise
   */
  getOutboundDetail(id) {
    return doGet(`/stock/outbound/bill/${id}`)
  },

  /**
   * 新增出库单
   * @param {Object} data - 出库单数据
   * @returns {Promise} 返回新增结果的 Promise
   */
  createOutbound(data) {
    return doPost('/stock/outbound/bill', data)
  },

  /**
   * 更新出库单
   * @param {Number} id - 出库单ID
   * @param {Object} data - 出库单数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateOutbound(id, data) {
    return doPut(`/stock/outbound/bill/${id}`, data)
  },

  /**
   * 删除出库单
   * @param {Number} id - 出库单 ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteOutbound(id) {
    return doDelete(`/stock/outbound/bill/${id}`)
  },

  /**
   * 批量删除出库单
   * @param {Array} ids - 出库单 ID 数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteOutbounds(ids) {
    return doPost('/stock/outbound/bill/batch', { ids })
  },

  /**
   * 获取出库单状态列表
   * @returns {Promise} 返回出库单状态列表的 Promise
   */
  getOutboundStatusList() {
    return doGet('/stock/outbound/bill/status')
  },

  /**
   * 更新出库单状态
   * @param {Number} id - 出库单ID
   * @param {String} status - 新状态
   * @returns {Promise} 返回状态更新结果的 Promise
   */
  updateOutboundStatus(id, status) {
    return doPut(`/stock/outbound/bill/${id}/status`, { status })
  },

  /**
   * 导出出库单数据
   * @param {Object} params - 查询参数，同getOutboundList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportOutbounds(params) {
    return doGet('/stock/outbound/bill/export', params, { responseType: 'blob' })
  },

  /**
   * 导出车辆出库数据
   * @param {Object} params - 查询参数，同getOutboundList
   * @returns {Promise} 返回导出结果的 Promise，包含file字段
   */
  exportVehicleOutbound(params) {
    return doGet('/vehicle/outbound/export', params)
  },

  /**
   * 导入出库单数据
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise} 返回导入结果的 Promise
   */
  importOutbounds(formData) {
    return doPost('/stock/outbound/bill/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default outboundApi

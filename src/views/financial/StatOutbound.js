import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import outboundApi from '@/api/outbound'
import { formatDate } from '@/utils/dateUtils'

import { NIcon, NTag } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  CopyOutline,
  DownloadOutline
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import OrderDetailModal from '@/components/orders/OrderDetailModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

export default function useStatOutbound() {
  // 状态变量
  const tableRef = ref(null)
  const loading = ref(false)
  const exporting = ref(false)

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动（无页面滚动模式）
  const tableMaxHeight = computed(() => {
    // 根据屏幕尺寸动态调整各组件高度
    const screenHeight = windowHeight.value

    // 基础组件高度
    const pagepadding = 32 // 页面上下padding
    let filterHeight, toolbarHeight, paginationHeight, margin

    // 根据屏幕高度动态调整
    if (screenHeight >= 1080) {
      // 大屏幕 (24寸等)
      filterHeight = 180
      toolbarHeight = 70
      paginationHeight = 60
      margin = 20
      return 800
    } else if (screenHeight >= 768) {
      // 中等屏幕
      filterHeight = 160
      toolbarHeight = 60
      paginationHeight = 50
      margin = 15
      return 360
    } else {
      // 小屏幕
      filterHeight = 140
      toolbarHeight = 50
      paginationHeight = 40
      margin = 10
    }

    // 计算表格容器的可用高度（不包括分页组件）
    const containerHeight =
      screenHeight -
      pagepadding -
      filterHeight -
      toolbarHeight -
      margin

    // 表格本身的高度需要减去分页组件的高度
    const tableHeight = containerHeight - paginationHeight

    // 动态最小高度和最大高度
    const minHeight = Math.min(250, screenHeight * 0.25)
    const maxHeight = screenHeight * 0.6 // 表格最大不超过屏幕高度的60%

    const finalHeight = Math.max(Math.min(tableHeight, maxHeight), minHeight)

    return finalHeight
  })

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    minAmount: null,
    maxAmount: null,
    keywords: ''
  })

  // 数据列表
  const ordersData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 表格数据
  const filteredData = computed(() => {
    return ordersData.value
  })

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择出库单位"
  })

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 表格列配置
  const columns = [
    {
      title: '车架号VIN',
      key: 'vin',
      width: 250,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.vin),
            title: '点击复制车架号',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.vin),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '出库时间',
      key: 'outboundTime',
            width: 260,

      render(row) {
        return row.outboundTime ? formatDate(row.outboundTime, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      title: '订单日期',
      key: 'orderDate',
      width: 260,
      render(row) {
        return row.orderDate ? formatDate(row.orderDate, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      title: '收款时间',
      key: 'receptTime',
            width: 260,

      render(row) {
        return row.receptTime ? formatDate(row.receptTime, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      title: '账户名称',
      key: 'accountName'
    },
    {
      title: '科目名称',
      key: 'feeName',
      width: 200
    },
    {
      title: '费用金额(元)',
      key: 'feeAmount',
      align: 'right',
      render(row) {
        if (!row.feeAmount && row.feeAmount !== 0) return '-'
        return `${(row.feeAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '订单编号',
      key: 'orderSn',
      width: 250,
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.orderSn),
            title: '点击复制订单号',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.orderSn),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '收款单位',
      key: 'receptOrgName'
    },
    {
      title: '客户名称',
      key: 'customerName'
    },
    {
      title: '联系方式',
      key: 'mobile',
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.mobile) return ''
        if (row.mobile.length === 11) {
          return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7)
        }
        return row.mobile
      }
    },
    {
      title: '销售单位',
      key: 'cusOwnerOrgName'
    },
    {
      title: '销售顾问',
      key: 'ownerSellerName'
    },
    {
      title: '客户分类',
      key: 'customerType'
    },
    {
      title: '品牌',
      key: 'brand'
    },
    {
      title: '车系',
      key: 'series'
    },
    {
      title: '车型',
      key: 'modelName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '配置',
      key: 'configName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '颜色',
      key: 'colorCode',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '启票价(元)',
      key: 'sbAmount',
      align: 'right',
      render(row) {
        if (!row.sbAmount && row.sbAmount !== 0) return '-'
        return `${(row.sbAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '启票单位',
      key: 'sbOrgName'
    },
    {
      title: '现金优惠(元)',
      key: 'discountAmount',
      align: 'right',
      render(row) {
        if (!row.discountAmount && row.discountAmount !== 0) return '-'
        return `${(row.discountAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '现金优惠转车款',
      key: 'discountDeductible',
      align: 'center',
            width: 160,

      render(row) {
        return row.discountDeductible ? '是' : '否'
      }
    },
    {
      title: '成交价(元)',
      key: 'dealAmount',
      align: 'right',
      render(row) {
        if (!row.dealAmount && row.dealAmount !== 0) return '-'
        return `${(row.dealAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '车辆售价(元)',
      key: 'salesAmount',
      align: 'right',
      render(row) {
        if (!row.salesAmount && row.salesAmount !== 0) return '-'
        return `${(row.salesAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '开票金额(元)',
      key: 'invoiceAmount',
      align: 'right',
      render(row) {
        if (!row.invoiceAmount && row.invoiceAmount !== 0) return '-'
        return `${(row.invoiceAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '付款方式',
      key: 'paymentMethod'
    },
    {
      title: '贷款渠道',
      key: 'loanChannel'
    },
    {
      title: '贷款期数',
      key: 'loanMonths',
      align: 'center'
    },
    {
      title: '贷款金额(元)',
      key: 'loanAmount',
      align: 'right',
      render(row) {
        if (!row.loanAmount && row.loanAmount !== 0) return '-'
        return `${(row.loanAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '首付金额(元)',
      key: 'loanInitialAmount',
      align: 'right',
      render(row) {
        if (!row.loanInitialAmount && row.loanInitialAmount !== 0) return '-'
        return `${(row.loanInitialAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '首付比例',
      key: 'loanInitialRatio',
      align: 'center',
      render(row) {
        if (!row.loanInitialRatio && row.loanInitialRatio !== 0) return '-'
        return `${(row.loanInitialRatio * 100).toFixed(2)}%`
      }
    },
    {
      title: '分期服务费(元)',
      key: 'loanFee',
      align: 'right',
      render(row) {
        if (!row.loanFee && row.loanFee !== 0) return '-'
        return `${(row.loanFee / 100).toFixed(2)}`
      }
    },
    {
      title: '应收机构分期返利(元)',
      key: 'loanRebateReceivableAmount',
      align: 'right',
      render(row) {
        if (!row.loanRebateReceivableAmount && row.loanRebateReceivableAmount !== 0) return '-'
        return `${(row.loanRebateReceivableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '应付客户分期返利(元)',
      key: 'loanRebatePayableAmount',
      align: 'right',
      render(row) {
        if (!row.loanRebatePayableAmount && row.loanRebatePayableAmount !== 0) return '-'
        return `${(row.loanRebatePayableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '分期返利转车款',
      key: 'loanRebatePayableDeductible',
      align: 'center',
      render(row) {
        return row.loanRebatePayableDeductible ? '是' : '否'
      }
    },
    {
      title: '二手车车牌号',
      key: 'usedVehicleId'
    },
    {
      title: '二手车VIN',
      key: 'usedVehicleVin',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '二手车品牌',
      key: 'usedVehicleBrand'
    },
    {
      title: '二手车车型',
      key: 'usedVehicleModel'
    },
    {
      title: '二手车颜色',
      key: 'usedVehicleColor'
    },
    {
      title: '置换金额(元)',
      key: 'usedVehicleAmount',
      align: 'right',
      render(row) {
        if (!row.usedVehicleAmount && row.usedVehicleAmount !== 0) return '-'
        return `${(row.usedVehicleAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '置换补贴(元)',
      key: 'usedVehicleDeductibleAmount',
      align: 'right',
      render(row) {
        if (!row.usedVehicleDeductibleAmount && row.usedVehicleDeductibleAmount !== 0) return '-'
        return `${(row.usedVehicleDeductibleAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '收厂家补贴金额(元)',
      key: 'usedVehicleDiscountReceivableAmount',
      align: 'right',
      render(row) {
        if (!row.usedVehicleDiscountReceivableAmount && row.usedVehicleDiscountReceivableAmount !== 0) return '-'
        return `${(row.usedVehicleDiscountReceivableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '付客户补贴金额(元)',
      key: 'usedVehicleDiscountPayableAmount',
      align: 'right',
      render(row) {
        if (!row.usedVehicleDiscountPayableAmount && row.usedVehicleDiscountPayableAmount !== 0) return '-'
        return `${(row.usedVehicleDiscountPayableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '置换补贴转车款',
      key: 'usedVehicleDiscountPayableDeductible',
      align: 'center',
      render(row) {
        return row.usedVehicleDiscountPayableDeductible ? '是' : '否'
      }
    },
    {
      title: '定金转车款',
      key: 'depositDeductible',
      align: 'center',
      render(row) {
        return row.depositDeductible ? '是' : '否'
      }
    },
    {
      title: '外卖装具收入(元)',
      key: 'deliveryEquipmentIncome',
      align: 'right',
      render(row) {
        if (!row.deliveryEquipmentIncome && row.deliveryEquipmentIncome !== 0) return '-'
        return `${(row.deliveryEquipmentIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '外卖装具成本(元)',
      key: 'deliveryEquipmentCost',
      align: 'right',
      render(row) {
        if (!row.deliveryEquipmentCost && row.deliveryEquipmentCost !== 0) return '-'
        return `${(row.deliveryEquipmentCost / 100).toFixed(2)}`
      }
    },
    {
      title: '是否有赠品',
      key: 'hasGiftItems',
      align: 'center',
      render(row) {
        return row.hasGiftItems === 'YES' ? '是' : '否'
      }
    },
    {
      title: '赠品总价(元)',
      key: 'giftItems',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '是否本司上险',
      key: 'hasInsurance',
      align: 'center',
      render(row) {
        return row.hasInsurance === 'YES' ? '是' : '否'
      }
    },
    {
      title: '试乘试驾-折旧金额(元)',
      key: 'depreciationAmount',
      align: 'right',
      render(row) {
        if (!row.depreciationAmount && row.depreciationAmount !== 0) return '-'
        return `${(row.depreciationAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '试乘试驾-厂家支持金额(元)',
      key: 'manufacturerSupportAmount',
      align: 'right',
      render(row) {
        if (!row.manufacturerSupportAmount && row.manufacturerSupportAmount !== 0) return '-'
        return `${(row.manufacturerSupportAmount / 100).toFixed(2)}`
      }
    },
    // 专项优惠相关
    {
      title: '专项优惠金额(元)',
      key: 'exclusiveDiscountPayableAmount',
      align: 'right',
      render(row) {
        if (!row.exclusiveDiscountPayableAmount && row.exclusiveDiscountPayableAmount !== 0) return '-'
        return `${(row.exclusiveDiscountPayableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '专项优惠应收金额(元)',
      key: 'exclusiveDiscountReceivableAmount',
      align: 'right',
      render(row) {
        if (!row.exclusiveDiscountReceivableAmount && row.exclusiveDiscountReceivableAmount !== 0) return '-'
        return `${(row.exclusiveDiscountReceivableAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '专项优惠类型',
      key: 'exclusiveDiscountType'
    },
    {
      title: '专项优惠抵车款',
      key: 'exclusiveDiscountPayableDeductible',
      align: 'center',
      render(row) {
        return row.exclusiveDiscountPayableDeductible ? '是' : '否'
      }
    },
    {
      title: '专项优惠说明',
      key: 'exclusiveDiscountRemark',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '是否存在专项优惠',
      key: 'hasExclusiveDiscount',
      align: 'center',
      render(row) {
        return row.hasExclusiveDiscount === 'YES' ? '是' : '否'
      }
    },
    // 库存相关
    {
      title: '库存金额(元)',
      key: 'stockAmount',
      align: 'right',
      render(row) {
        if (!row.stockAmount && row.stockAmount !== 0) return '-'
        return `${(row.stockAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '库龄天数',
      key: 'stockDays',
      align: 'center'
    },
    {
      title: '仓储机构名称',
      key: 'stockOrgName'
    },
    {
      title: '试乘试驾状态',
      key: 'trialStatus'
    },
    {
      title: '库存类型',
      key: 'stockType'
    },
    {
      title: '销售地类型',
      key: 'salesStoreType'
    },
    // 衍生费用相关
    {
      title: '公证费收入(元)',
      key: 'notaryFeeIncome',
      align: 'right',
      render(row) {
        if (!row.notaryFeeIncome && row.notaryFeeIncome !== 0) return '-'
        return `${(row.notaryFeeIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '公证费成本(元)',
      key: 'notaryFeeCost',
      align: 'right',
      render(row) {
        if (!row.notaryFeeCost && row.notaryFeeCost !== 0) return '-'
        return `${(row.notaryFeeCost / 100).toFixed(2)}`
      }
    },
    {
      title: '畅行无忧收入(元)',
      key: 'dcCarefreeIncome',
      align: 'right',
      render(row) {
        if (!row.dcCarefreeIncome && row.dcCarefreeIncome !== 0) return '-'
        return `${(row.dcCarefreeIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '畅行无忧成本(元)',
      key: 'carefreeCost',
      align: 'right',
      render(row) {
        if (!row.carefreeCost && row.carefreeCost !== 0) return '-'
        return `${(row.carefreeCost / 100).toFixed(2)}`
      }
    },
    {
      title: '延保收入(元)',
      key: 'dcExtendedWarrantyIncome',
      align: 'right',
      render(row) {
        if (!row.dcExtendedWarrantyIncome && row.dcExtendedWarrantyIncome !== 0) return '-'
        return `${(row.dcExtendedWarrantyIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '延保成本(元)',
      key: 'extendedWarrantyCost',
      align: 'right',
      render(row) {
        if (!row.extendedWarrantyCost && row.extendedWarrantyCost !== 0) return '-'
        return `${(row.extendedWarrantyCost / 100).toFixed(2)}`
      }
    },
    {
      title: 'VPS收入(元)',
      key: 'dcVpsIncome',
      align: 'right',
      render(row) {
        if (!row.dcVpsIncome && row.dcVpsIncome !== 0) return '-'
        return `${(row.dcVpsIncome / 100).toFixed(2)}`
      }
    },
    {
      title: 'VPS成本(元)',
      key: 'vpsCost',
      align: 'right',
      render(row) {
        if (!row.vpsCost && row.vpsCost !== 0) return '-'
        return `${(row.vpsCost / 100).toFixed(2)}`
      }
    },
    {
      title: '预收利息收入(元)',
      key: 'preInterestIncome',
      align: 'right',
      render(row) {
        if (!row.preInterestIncome && row.preInterestIncome !== 0) return '-'
        return `${(row.preInterestIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '预收利息成本(元)',
      key: 'preInterestCost',
      align: 'right',
      render(row) {
        if (!row.preInterestCost && row.preInterestCost !== 0) return '-'
        return `${(row.preInterestCost / 100).toFixed(2)}`
      }
    },
    {
      title: '牌照费收入(元)',
      key: 'licensePlateFeeIncome',
      align: 'right',
      render(row) {
        if (!row.licensePlateFeeIncome && row.licensePlateFeeIncome !== 0) return '-'
        return `${(row.licensePlateFeeIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '牌照费成本(元)',
      key: 'licensePlateFeeCost',
      align: 'right',
      render(row) {
        if (!row.licensePlateFeeCost && row.licensePlateFeeCost !== 0) return '-'
        return `${(row.licensePlateFeeCost / 100).toFixed(2)}`
      }
    },
    {
      title: '临牌费收入(元)',
      key: 'tempPlateFeeIncome',
      align: 'right',
      render(row) {
        if (!row.tempPlateFeeIncome && row.tempPlateFeeIncome !== 0) return '-'
        return `${(row.tempPlateFeeIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '临牌费成本(元)',
      key: 'tempPlateFeeCost',
      align: 'right',
      render(row) {
        if (!row.tempPlateFeeCost && row.tempPlateFeeCost !== 0) return '-'
        return `${(row.tempPlateFeeCost / 100).toFixed(2)}`
      }
    },
    {
      title: '其他收入(元)',
      key: 'dcOtherIncome',
      align: 'right',
      render(row) {
        if (!row.dcOtherIncome && row.dcOtherIncome !== 0) return '-'
        return `${(row.dcOtherIncome / 100).toFixed(2)}`
      }
    },
    {
      title: '其他成本(元)',
      key: 'otherCost',
      align: 'right',
      render(row) {
        if (!row.otherCost && row.otherCost !== 0) return '-'
        return `${(row.otherCost / 100).toFixed(2)}`
      }
    },
    // 保险相关
    {
      title: '交强险保费(元)',
      key: 'ciFee',
      align: 'right',
      render(row) {
        if (!row.ciFee && row.ciFee !== 0) return '-'
        return `${(row.ciFee / 100).toFixed(2)}`
      }
    },
    {
      title: '交强险返利(元)',
      key: 'ciRebate',
      align: 'right',
      render(row) {
        if (!row.ciRebate && row.ciRebate !== 0) return '-'
        return `${(row.ciRebate / 100).toFixed(2)}`
      }
    },
    {
      title: '商业险保费(元)',
      key: 'biFee',
      align: 'right',
      render(row) {
        if (!row.biFee && row.biFee !== 0) return '-'
        return `${(row.biFee / 100).toFixed(2)}`
      }
    },
    {
      title: '商业险返利(元)',
      key: 'biRebate',
      align: 'right',
      render(row) {
        if (!row.biRebate && row.biRebate !== 0) return '-'
        return `${(row.biRebate / 100).toFixed(2)}`
      }
    },
    {
      title: '非车险保费(元)',
      key: 'oiFee',
      align: 'right',
      render(row) {
        if (!row.oiFee && row.oiFee !== 0) return '-'
        return `${(row.oiFee / 100).toFixed(2)}`
      }
    },
    {
      title: '非车险返利(元)',
      key: 'oiRebate',
      align: 'right',
      render(row) {
        if (!row.oiRebate && row.oiRebate !== 0) return '-'
        return `${(row.oiRebate / 100).toFixed(2)}`
      }
    },
    {
      title: '返利状态',
      key: 'rebateStatus'
    }
  ]

  // 表格横向滚动宽度 - 根据列数量计算
  const scrollX = computed(() => {
    // 计算所有列的总宽度
    // VIN列(250) + 其他列的宽度总和
    // 由于字段非常多（100+个字段），设置一个足够大的滚动宽度
    // 粗略估算：100个字段 * 平均120px = 12000px
    return 12000
  })

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.beginDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.brand = filterForm.vehicleCategory
      }

      // 处理出库单位 - 支持多选，使用机构ID的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id，以逗号分隔的格式传入
        params.outboundOrgId = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",")
      }

      // 始终查询已出库状态的数据
      params.outboundStatus = 'COMPLETED'

      // 调用API获取数据
      const response = await outboundApi.getOutboundList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        ordersData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id
    detailDialogVisible.value = true
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理页面大小变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    )
    handleSearch()
  }

  // 导出数据
  const handleExport = async () => {
    exporting.value = true
    try {
      // 构建查询参数，与refreshData方法保持一致
      const params = {}

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.vehicleCategory = filterForm.vehicleCategory
      }

      // 处理机构筛选
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        params.invoiceOrgIds = filterForm.invoiceOrgs.map(org => org.id)
      }

      // 始终查询已出库状态的数据
      params.outboundStatus = 'COMPLETED'

      // 调用导出API
      const response = await outboundApi.exportVehicleOutbound(params)

      if (response.code === 200 && response.data && response.data.file) {
        // 创建下载链接
        const link = document.createElement('a')
        link.href = response.data.file
        link.target = '_blank'
        link.download = `车辆出库数据_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        messages.success('导出成功')
      } else {
        messages.error(response.message || '导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      messages.error('导出失败，请重试')
    } finally {
      exporting.value = false
    }
  }

  // 生命周期方法
  const initialize = async () => {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
    // 初始化数据
    refreshData()
  }

  const cleanup = () => {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 组件引用
    OrderDetailModal,
    BizOrgSelector,

    // 图标
    SearchOutline,
    RefreshOutline,
    DownloadOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    exporting,
    filterForm,
    ordersData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailId,
    showOrgSelector,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    scrollX,
    selectedOrgText,

    // 日期相关
    dateRangeOptions,

    // 业务方法
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    handleView,
    handlePageChange,
    handlePageSizeChange,
    refreshData,
    handleExport,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 生命周期方法
    initialize,
    cleanup
  }
}

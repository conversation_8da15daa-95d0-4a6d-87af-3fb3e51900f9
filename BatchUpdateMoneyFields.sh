#!/bin/bash

# 批量更新StatResultVO.java中的金额字段
# 将分转换为元显示

FILE="StatResultVO.java"

# 备份原文件
cp "$FILE" "${FILE}.backup"

# 批量替换金额字段的注解
# 1. 将 "(分)" 替换为 "(元)"
sed -i 's/(分)/(元)/g' "$FILE"

# 2. 为金额字段添加转换器
# 这个需要手动处理，因为需要识别具体的金额字段

echo "已完成基础替换，请手动添加以下内容到金额字段："
echo "1. 在@ExcelProperty注解中添加: , converter = MoneyConverter.class"
echo "2. 将@NumberFormat(\"#\")替换为: @NumberFormat(\"#0.00\")"
echo "3. 添加: @ColumnWidth(15)"

echo ""
echo "示例："
echo "原："
echo "@ExcelProperty(value = \"费用金额(元)\", index = 3)"
echo "@NumberFormat(\"#\")"
echo "private long feeAmount;"
echo ""
echo "改为："
echo "@ExcelProperty(value = \"费用金额(元)\", index = 3, converter = MoneyConverter.class)"
echo "@NumberFormat(\"#0.00\")"
echo "@ColumnWidth(15)"
echo "private long feeAmount;"
